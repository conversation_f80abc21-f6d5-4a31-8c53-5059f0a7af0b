/* pages/index/index.wxss */
.index-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding: 32rpx;
}

.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  color: white;
}

.balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.greeting {
  font-size: 36rpx;
  font-weight: 500;
}

.date {
  font-size: 26rpx;
  opacity: 0.8;
}

.balance-amount {
  display: flex;
  align-items: baseline;
  margin-bottom: 16rpx;
}

.currency {
  font-size: 40rpx;
  margin-right: 8rpx;
}

.amount {
  font-size: 72rpx;
  font-weight: bold;
}

.balance-label {
  font-size: 30rpx;
  opacity: 0.8;
  margin-bottom: 40rpx;
}

.balance-label-row {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8rpx;
}

.balance-text {
  margin-right: 16rpx;
}

.eye-toggle {
  font-size: 32rpx;
  padding: 8rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  min-width: 48rpx;
  min-height: 48rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10;
}

.tap-hint {
  font-size: 24rpx;
  opacity: 0.6;
  margin-left: 16rpx;
}

.refresh-btn {
  font-size: 28rpx;
  margin-left: 16rpx;
  opacity: 0.8;
  padding: 8rpx;
  color: #007AFF;
}

.balance-content {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.balance-content:active {
  transform: scale(0.98);
}

.balance-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.stat-item:active {
  transform: scale(0.95);
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.stat-value.income {
  color: #4ADE80;
}

.stat-value.expense {
  color: #F87171;
}

.stat-label {
  font-size: 26rpx;
  opacity: 0.8;
}

.quick-actions {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 32rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.income-icon {
  background-color: #34C759;
}

.expense-icon {
  background-color: #FF3B30;
}

.transfer-icon {
  background-color: #007AFF;
}

.analytics-icon {
  background-color: #AF52DE;
}

.icon-text {
  font-size: 40rpx;
}

.action-title {
  font-size: 26rpx;
  color: #000000;
  text-align: center;
}

.recent-transactions {
  
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.more-btn {
  color: #007AFF;
  font-size: 26rpx;
}

.transactions-list {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.transaction-info {
  flex: 1;
}

.transaction-desc {
  display: block;
  font-size: 34rpx;
  color: #000000;
  margin-bottom: 4rpx;
}

.transaction-category {
  font-size: 26rpx;
  color: #8E8E93;
}

.transaction-right {
  text-align: right;
}

.transaction-amount {
  display: block;
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.transaction-amount.income {
  color: #34C759;
}

.transaction-amount.expense {
  color: #FF3B30;
}

.transaction-date {
  font-size: 26rpx;
  color: #8E8E93;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #FFFFFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
}

.empty-hint {
  font-size: 26rpx;
  color: #C7C7CC;
}

/* 加载中的交易项样式 */
.loading-transactions {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
}

.loading-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.loading-item:last-child {
  border-bottom: none;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #F2F2F7;
  margin-right: 32rpx;
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-content {
  flex: 1;
}

.loading-line {
  height: 24rpx;
  background-color: #F2F2F7;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-line.short {
  width: 60%;
}

.loading-line.long {
  width: 40%;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 深色模式样式 */
.dark-mode .index-page {
  background-color: #1C1C1E !important;
}

.dark-mode .balance-card {
  background-color: #2C2C2E !important;
}

.dark-mode .greeting {
  color: #FFFFFF !important;
}

.dark-mode .date {
  color: #8E8E93 !important;
}

.dark-mode .balance-amount {
  color: #FFFFFF !important;
}

.dark-mode .balance-label {
  color: #8E8E93 !important;
}

.dark-mode .stats-item {
  background-color: #3A3A3C !important;
}

.dark-mode .stats-amount {
  color: #FFFFFF !important;
}

.dark-mode .stats-label {
  color: #8E8E93 !important;
}

.dark-mode .stats-amount.income {
  color: #30D158 !important;
}

.dark-mode .stats-amount.expense {
  color: #FF453A !important;
}

.dark-mode .section-title {
  color: #FFFFFF !important;
}

.dark-mode .more-btn {
  color: #0A84FF !important;
}

.dark-mode .transactions-list {
  background-color: #2C2C2E !important;
}

.dark-mode .transaction-item {
  border-bottom-color: #3A3A3C !important;
}

.dark-mode .transaction-icon {
  background-color: #3A3A3C !important;
}

.dark-mode .transaction-desc {
  color: #FFFFFF !important;
}

.dark-mode .transaction-category {
  color: #8E8E93 !important;
}

.dark-mode .transaction-amount.income {
  color: #30D158 !important;
}

.dark-mode .transaction-amount.expense {
  color: #FF453A !important;
}

.dark-mode .transaction-date {
  color: #8E8E93 !important;
}

.dark-mode .empty-state {
  background-color: #2C2C2E !important;
}

.dark-mode .empty-text {
  color: #8E8E93 !important;
}

.dark-mode .empty-hint {
  color: #636366 !important;
}

.dark-mode .loading-transactions {
  background-color: #2C2C2E !important;
}

.dark-mode .loading-item {
  border-bottom-color: #3A3A3C !important;
}

.dark-mode .loading-icon {
  background-color: #3A3A3C !important;
}

.dark-mode .loading-line {
  background-color: #3A3A3C !important;
}
