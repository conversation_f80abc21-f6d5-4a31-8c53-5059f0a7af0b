<!-- 设置页面 -->
<view class="settings-page {{isDarkMode ? 'dark-mode' : ''}}"
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-avatar">
      <text class="avatar-text">{{userInfo.username ? userInfo.username.charAt(0).toUpperCase() : 'U'}}</text>
    </view>
    <view class="user-info">
      <view class="username">{{userInfo.username || '未登录'}}</view>
      <view class="user-email">{{userInfo.email || ''}}</view>
    </view>
    <text class="edit-btn" bindtap="editProfile">编辑</text>
  </view>
  
  <!-- 设置列表 -->
  <view class="settings-list">
    <!-- 账户设置 -->
    <view class="list-section">
      <view class="section-header">账户设置</view>
      <view class="section-content">
        <view class="list-item" bindtap="goToProfile">
          <text class="cell-icon">👤</text>
          <text class="cell-title">个人信息</text>
          <text class="cell-arrow">></text>
        </view>
        
        <view class="list-item" bindtap="goToPreferences">
          <text class="cell-icon">⚙️</text>
          <text class="cell-title">偏好设置</text>
          <text class="cell-arrow">></text>
        </view>
      </view>
    </view>
    
    <!-- 应用设置 -->
    <view class="list-section">
      <view class="section-header">应用设置</view>
      <view class="section-content">
        <view class="list-item" bindtap="clearCache">
          <text class="cell-icon">🗑️</text>
          <text class="cell-title">清除缓存</text>
          <text class="cell-value">{{cacheSize}}</text>
        </view>
        
        <view class="list-item" bindtap="showHelp">
          <text class="cell-icon">❓</text>
          <text class="cell-title">使用帮助</text>
          <text class="cell-arrow">></text>
        </view>

        <view class="list-item" bindtap="goToAbout">
          <text class="cell-icon">ℹ️</text>
          <text class="cell-title">关于应用</text>
          <text class="cell-arrow">></text>
        </view>

        <!-- 调试入口，仅开发环境显示 -->
        <view class="list-item" bindtap="goToDebug" wx:if="{{true}}">
          <text class="cell-icon">🐛</text>
          <text class="cell-title">调试信息</text>
          <text class="cell-arrow">></text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 退出登录按钮 -->
  <view class="logout-section">
    <button class="btn-danger logout-btn" bindtap="handleLogout">
      退出登录
    </button>
  </view>
</view>
