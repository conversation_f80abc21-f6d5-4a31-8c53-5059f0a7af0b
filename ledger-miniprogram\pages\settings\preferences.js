// pages/settings/preferences.js
const api = require('../../utils/api.js')
const darkMode = require('../../utils/darkMode.js')

Page({
  data: {
    preferences: {
      darkMode: false,
      currency: 'CNY',
      decimalPlaces: 2,
      quickTransaction: true,
      defaultTransactionType: 'expense',
      dailyReminder: false,
      autoBackup: true,

    },
    isDarkMode: false, // 添加深色模式状态
    saving: false,
    showCurrencyModal: false,
    currencyOptions: [
      { code: 'CNY', name: '人民币' },
      { code: 'USD', name: '美元' },
      { code: 'EUR', name: '欧元' },
      { code: 'JPY', name: '日元' },
      { code: 'GBP', name: '英镑' },
      { code: 'HKD', name: '港币' },
      { code: 'TWD', name: '台币' },
      { code: 'KRW', name: '韩元' }
    ]
  },

  onLoad() {
    console.log('偏好设置页面加载')
    // 应用深色模式到当前页面
    darkMode.applyDarkModeToPage(this)
    this.loadPreferences()
  },

  // 加载偏好设置
  async loadPreferences() {
    try {
      // 先从本地存储加载
      const localPreferences = wx.getStorageSync('preferences')
      if (localPreferences) {
        const parsedPreferences = { ...this.data.preferences, ...JSON.parse(localPreferences) }
        this.setData({
          preferences: parsedPreferences,
          isDarkMode: parsedPreferences.darkMode || false
        })
      }

      // 从服务器获取最新设置
      const serverSettings = await api.auth.getSettings()
      if (serverSettings) {
        // 将服务器设置映射到本地偏好设置格式
        const mappedPreferences = {
          darkMode: serverSettings.dark_mode || false,
          currency: serverSettings.currency || 'CNY',
          decimalPlaces: 2, // 服务器暂时没有这个字段
          quickTransaction: true, // 服务器暂时没有这个字段
          defaultTransactionType: 'expense', // 服务器暂时没有这个字段
          dailyReminder: serverSettings.notifications || false,
          autoBackup: serverSettings.auto_backup || false,

        }

        this.setData({
          preferences: mappedPreferences,
          isDarkMode: mappedPreferences.darkMode || false
        })

        // 更新本地存储
        wx.setStorageSync('preferences', JSON.stringify(mappedPreferences))
      }

      // 应用当前的偏好设置
      this.applyPreferences()
    } catch (error) {
      console.error('加载偏好设置失败:', error)
      // 如果服务器请求失败，继续使用本地设置
      this.applyPreferences()
    }
  },

  // 深色模式切换
  onDarkModeChange(e) {
    const isDark = e.detail.value
    this.setData({
      'preferences.darkMode': isDark,
      isDarkMode: isDark
    })

    // 使用深色模式工具设置状态
    darkMode.setDarkMode(isDark)
  },

  // 快速记账切换
  onQuickTransactionChange(e) {
    this.setData({
      'preferences.quickTransaction': e.detail.value
    })
  },

  // 记账提醒切换
  onDailyReminderChange(e) {
    this.setData({
      'preferences.dailyReminder': e.detail.value
    })
    
    if (e.detail.value) {
      // 请求通知权限
      wx.requestSubscribeMessage({
        tmplIds: ['your_template_id'], // 需要替换为实际的模板ID
        success: (res) => {
          console.log('订阅消息成功:', res)
        },
        fail: (error) => {
          console.error('订阅消息失败:', error)
          wx.showToast({
            title: '请在设置中开启通知权限',
            icon: 'none'
          })
        }
      })
    }
  },

  // 自动备份切换
  onAutoBackupChange(e) {
    this.setData({
      'preferences.autoBackup': e.detail.value
    })
  },





  // 选择货币
  selectCurrency() {
    this.setData({
      showCurrencyModal: true
    })
  },

  // 关闭货币选择弹窗
  closeCurrencyModal() {
    this.setData({
      showCurrencyModal: false
    })
  },

  // 选择货币选项
  selectCurrencyOption(e) {
    const currency = e.currentTarget.dataset.currency
    this.setData({
      'preferences.currency': currency,
      showCurrencyModal: false
    })
  },

  // 选择小数位数
  selectDecimalPlaces() {
    wx.showActionSheet({
      itemList: ['0位', '1位', '2位', '3位'],
      success: (res) => {
        this.setData({
          'preferences.decimalPlaces': res.tapIndex
        })
      }
    })
  },

  // 选择默认交易类型
  selectDefaultTransactionType() {
    wx.showActionSheet({
      itemList: ['支出', '收入'],
      success: (res) => {
        const type = res.tapIndex === 0 ? 'expense' : 'income'
        this.setData({
          'preferences.defaultTransactionType': type
        })
      }
    })
  },

  // 立即备份
  async backupData() {
    try {
      wx.showLoading({
        title: '备份中...',
        mask: true
      })

      const result = await api.user.backupData()

      wx.hideLoading()
      wx.showToast({
        title: '备份成功',
        icon: 'success'
      })

      console.log('备份完成:', result)
    } catch (error) {
      wx.hideLoading()
      console.error('备份失败:', error)
      wx.showToast({
        title: '备份失败',
        icon: 'none'
      })
    }
  },

  // 导出数据
  async exportData() {
    try {
      wx.showLoading({
        title: '导出中...',
        mask: true
      })

      const result = await api.user.exportData()

      wx.hideLoading()

      // 显示导出结果摘要
      const data = result.data
      const summary = `导出完成！\n\n` +
        `交易记录: ${data.transactions?.length || 0} 条\n` +
        `账户: ${data.accounts?.length || 0} 个\n` +
        `分类: ${data.categories?.length || 0} 个\n` +
        `预算: ${data.budgets?.length || 0} 个\n\n` +
        `数据已保存到本地，如需Excel格式请使用网页版`

      wx.showModal({
        title: '导出成功',
        content: summary,
        confirmText: '确定',
        showCancel: false
      })

      // 将导出数据保存到本地存储（可选）
      wx.setStorageSync('exportedData', JSON.stringify(data))
      console.log('导出完成:', data)

    } catch (error) {
      wx.hideLoading()
      console.error('导出失败:', error)
      wx.showToast({
        title: error.message || '导出失败',
        icon: 'none'
      })
    }
  },

  // 恢复数据
  restoreData() {
    wx.showModal({
      title: '恢复数据',
      content: '将使用最新备份恢复数据，此操作将覆盖当前所有数据，是否继续？',
      confirmText: '确认恢复',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '恢复中...',
              mask: true
            })

            await api.user.restoreLatestBackup()

            wx.hideLoading()
            wx.showModal({
              title: '恢复成功',
              content: '数据恢复成功，请重新启动小程序以加载新数据',
              showCancel: false,
              success: () => {
                // 可以考虑重新加载页面或返回首页
                wx.reLaunch({
                  url: '/pages/index/index'
                })
              }
            })
          } catch (error) {
            wx.hideLoading()
            console.error('恢复失败:', error)
            wx.showToast({
              title: error.message || '恢复失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 导入数据
  importData() {
    wx.showModal({
      title: '导入数据',
      content: '从备份文件恢复数据，此操作将覆盖当前所有数据，是否继续？',
      confirmText: '选择文件',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 小程序暂不支持文件选择，显示提示
          wx.showToast({
            title: '请使用网页版进行文件导入',
            icon: 'none',
            duration: 3000
          })
        }
      }
    })
  },

  // 保存偏好设置
  async savePreferences() {
    this.setData({ saving: true })

    try {
      // 保存到本地存储
      wx.setStorageSync('preferences', JSON.stringify(this.data.preferences))

      // 将本地偏好设置映射到服务器设置格式
      const serverSettings = {
        language: 'zh',
        currency: this.data.preferences.currency,
        dark_mode: this.data.preferences.darkMode,
        notifications: this.data.preferences.dailyReminder,
        auto_backup: this.data.preferences.autoBackup
      }

      // 保存到服务器
      await api.auth.updateSettings(serverSettings)

      wx.showToast({
        title: '设置已保存',
        icon: 'success'
      })

      // 应用设置
      this.applyPreferences()

    } catch (error) {
      console.error('保存偏好设置失败:', error)
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'none'
      })
    } finally {
      this.setData({ saving: false })
    }
  },

  // 应用偏好设置
  applyPreferences() {
    const { preferences } = this.data

    // 应用深色模式
    darkMode.setDarkMode(preferences.darkMode)
    darkMode.applyDarkModeToPage(this)

    if (preferences.darkMode) {
      console.log('应用深色模式')
    } else {
      console.log('应用浅色模式')
    }

    // 设置默认货币
    wx.setStorageSync('defaultCurrency', preferences.currency)

    // 其他设置的应用逻辑...
  }
})
