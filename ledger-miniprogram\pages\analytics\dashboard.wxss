/* pages/analytics/dashboard.wxss */
.analytics-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding: 32rpx;
}

.time-selector {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.month-picker {
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.detail-btn {
  width: 64rpx;
  height: 64rpx;
  background-color: #007AFF;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.detail-icon {
  font-size: 32rpx;
  color: white;
}

.eye-toggle-btn {
  width: 64rpx;
  height: 64rpx;
  background-color: #34C759;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.eye-icon {
  font-size: 32rpx;
  color: white;
}

.month-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  margin-right: 16rpx;
}

.picker-arrow {
  font-size: 24rpx;
  color: #8E8E93;
}

.monthly-overview {
  display: flex;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.overview-item {
  flex: 1;
  text-align: center;
}

.overview-label {
  display: block;
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.overview-value {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
}

.overview-value.income {
  color: #34C759;
}

.overview-value.expense {
  color: #FF3B30;
}

.category-stats {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.section-header {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.loading {
  text-align: center;
  padding: 40rpx 0;
  color: #8E8E93;
  font-size: 30rpx;
}

.empty-state {
  text-align: center;
  padding: 40rpx 0;
  color: #8E8E93;
  font-size: 30rpx;
}

.categories-list {
  
}

.category-item {
  margin-bottom: 32rpx;
}

.category-item:last-child {
  margin-bottom: 0;
}

.category-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.category-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.category-name {
  flex: 1;
  font-size: 32rpx;
  color: #000000;
}

.category-stats-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.category-amount {
  font-size: 30rpx;
  font-weight: 600;
  color: #000000;
}

.category-percent {
  font-size: 26rpx;
  color: #8E8E93;
}

.category-bar {
  height: 8rpx;
  background-color: #E5E5EA;
  border-radius: 4rpx;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background-color: #FF3B30;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.income-bar {
  background-color: #34C759;
}

/* 趋势图样式 */
.trend-stats {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.trend-legend {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 24rpx;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
  margin-right: 12rpx;
}

.income-color {
  background-color: #34C759;
}

.expense-color {
  background-color: #FF3B30;
}

.legend-text {
  font-size: 26rpx;
  color: #8E8E93;
}

.trend-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 300rpx;
  padding: 0 16rpx;
}

.trend-month {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 8rpx;
}

.month-bars {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 100%;
  margin-bottom: 16rpx;
}

.bar-container {
  display: flex;
  align-items: flex-end;
  height: 200rpx;
  width: 60rpx;
}

.income-bar-trend {
  width: 24rpx;
  background-color: #34C759;
  border-radius: 4rpx 4rpx 0 0;
  margin-right: 4rpx;
  min-height: 8rpx;
  transition: height 0.3s ease;
}

.expense-bar-trend {
  width: 24rpx;
  background-color: #FF3B30;
  border-radius: 4rpx 4rpx 0 0;
  margin-left: 4rpx;
  min-height: 8rpx;
  transition: height 0.3s ease;
}

.month-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.month-amounts {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.income-amount {
  font-size: 20rpx;
  color: #34C759;
  margin-bottom: 4rpx;
}

.expense-amount {
  font-size: 20rpx;
  color: #FF3B30;
}

/* 预算对比样式 */
.budget-comparison {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.budget-header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.budget-summary {
  display: flex;
  align-items: center;
}

.budget-manage-btn {
  background-color: #007AFF;
  border-radius: 12rpx;
  padding: 12rpx 24rpx;
  transition: background-color 0.2s ease;
}

.budget-manage-btn:active {
  background-color: #0056CC;
}

.manage-text {
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 500;
}

.budget-usage {
  font-size: 32rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background-color: #F2F2F7;
}

.budget-usage.safe {
  color: #34C759;
  background-color: #E8F5E8;
}

.budget-usage.warning {
  color: #FF9500;
  background-color: #FFF4E6;
}

.budget-usage.danger {
  color: #FF3B30;
  background-color: #FFE6E6;
}

.budget-list {

}

.budget-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background-color: #F8F9FA;
  border-radius: 16rpx;
}

.budget-item:last-child {
  margin-bottom: 0;
}

.budget-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.budget-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.budget-details {
  flex: 1;
}

.budget-name {
  display: block;
  font-size: 30rpx;
  color: #000000;
  margin-bottom: 8rpx;
}

.budget-progress {
  display: block;
  font-size: 26rpx;
  color: #8E8E93;
}

.budget-status {
  width: 120rpx;
  text-align: right;
}

.usage-percent {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.usage-percent.safe {
  color: #34C759;
}

.usage-percent.warning {
  color: #FF9500;
}

.usage-percent.danger {
  color: #FF3B30;
}

.budget-progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #E5E5EA;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-fill.safe {
  background-color: #34C759;
}

.progress-fill.warning {
  background-color: #FF9500;
}

.progress-fill.danger {
  background-color: #FF3B30;
}

.empty-action {
  margin-top: 16rpx;
  text-align: center;
}

.action-link {
  color: #007AFF;
  font-size: 28rpx;
  text-decoration: underline;
}

/* 深色模式样式 */
.dark-mode .analytics-page {
  background-color: #1C1C1E !important;
}

.dark-mode .time-selector {
  background-color: #2C2C2E !important;
}

.dark-mode .month-text {
  color: #FFFFFF !important;
}

.dark-mode .picker-arrow {
  color: #8E8E93 !important;
}

.dark-mode .monthly-overview {
  background-color: #2C2C2E !important;
}

.dark-mode .overview-label {
  color: #8E8E93 !important;
}

.dark-mode .overview-value {
  color: #FFFFFF !important;
}

.dark-mode .overview-value.income {
  color: #30D158 !important;
}

.dark-mode .overview-value.expense {
  color: #FF453A !important;
}

.dark-mode .budget-comparison {
  background-color: #2C2C2E !important;
}

.dark-mode .section-title {
  color: #FFFFFF !important;
}

.dark-mode .budget-usage {
  color: #FFFFFF !important;
}

.dark-mode .budget-usage.safe {
  color: #30D158 !important;
}

.dark-mode .budget-usage.warning {
  color: #FF9F0A !important;
}

.dark-mode .budget-usage.danger {
  color: #FF453A !important;
}

.dark-mode .manage-text {
  color: #0A84FF !important;
}

.dark-mode .budget-item {
  border-bottom-color: #3A3A3C !important;
}

.dark-mode .budget-icon {
  background-color: #3A3A3C !important;
}

.dark-mode .budget-name {
  color: #FFFFFF !important;
}

.dark-mode .budget-progress {
  color: #8E8E93 !important;
}

.dark-mode .usage-percent.safe {
  color: #30D158 !important;
}

.dark-mode .usage-percent.warning {
  color: #FF9F0A !important;
}

.dark-mode .usage-percent.danger {
  color: #FF453A !important;
}

.dark-mode .budget-progress-bar {
  background-color: #3A3A3C !important;
}

.dark-mode .progress-fill.safe {
  background-color: #30D158 !important;
}

.dark-mode .progress-fill.warning {
  background-color: #FF9F0A !important;
}

.dark-mode .progress-fill.danger {
  background-color: #FF453A !important;
}

.dark-mode .action-link {
  color: #0A84FF !important;
}
