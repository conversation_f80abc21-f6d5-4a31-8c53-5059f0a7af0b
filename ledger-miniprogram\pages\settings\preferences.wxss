/* pages/settings/preferences.wxss */
.preferences-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding: 32rpx;
}

.settings-section {
  margin-bottom: 40rpx;
}

.section-header {
  padding: 0 32rpx 16rpx;
  font-size: 26rpx;
  color: #8E8E93;
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.section-content {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-title {
  display: block;
  font-size: 34rpx;
  color: #000000;
  margin-bottom: 8rpx;
}

.setting-desc {
  font-size: 26rpx;
  color: #8E8E93;
}

.setting-arrow {
  font-size: 32rpx;
  color: #C7C7CC;
  font-weight: 500;
  margin-left: 16rpx;
}

.save-section {
  margin-top: 40rpx;
}

.btn-primary {
  background-color: #007AFF;
  color: white;
  border-radius: 20rpx;
  padding: 24rpx 32rpx;
  font-size: 34rpx;
  border: none;
  width: 100%;
}

.btn-primary:active {
  background-color: #0056CC;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.modal-close {
  font-size: 48rpx;
  color: #8E8E93;
  line-height: 1;
}

.modal-body {
  max-height: 60vh;
  overflow-y: auto;
}

.currency-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5EA;
}

.currency-item:last-child {
  border-bottom: none;
}

.currency-item.selected {
  background-color: #F0F8FF;
}

.currency-code {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  width: 120rpx;
}

.currency-name {
  flex: 1;
  font-size: 30rpx;
  color: #666666;
}

.currency-check {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: bold;
}

/* 深色模式样式 */
.dark-mode {
  background-color: #1C1C1E !important;
}

.dark-mode .preferences-page {
  background-color: #1C1C1E !important;
}

.dark-mode .section-header {
  color: #8E8E93 !important;
}

.dark-mode .section-content {
  background-color: #2C2C2E !important;
}

.dark-mode .setting-item {
  border-bottom-color: #3A3A3C !important;
}

.dark-mode .setting-title {
  color: #FFFFFF !important;
}

.dark-mode .setting-desc {
  color: #8E8E93 !important;
}

.dark-mode .setting-arrow {
  color: #8E8E93 !important;
}

.dark-mode .btn-primary {
  background-color: #0A84FF !important;
}

.dark-mode .btn-primary:active {
  background-color: #0056CC !important;
}

.dark-mode .modal-content {
  background-color: #2C2C2E !important;
}

.dark-mode .modal-header {
  border-bottom-color: #3A3A3C !important;
}

.dark-mode .modal-title {
  color: #FFFFFF !important;
}

.dark-mode .modal-close {
  color: #8E8E93 !important;
}

.dark-mode .currency-item {
  border-bottom-color: #3A3A3C !important;
}

.dark-mode .currency-item.selected {
  background-color: #1C3A5E !important;
}

.dark-mode .currency-code {
  color: #FFFFFF !important;
}

.dark-mode .currency-name {
  color: #8E8E93 !important;
}

.dark-mode .currency-check {
  color: #0A84FF !important;
}
