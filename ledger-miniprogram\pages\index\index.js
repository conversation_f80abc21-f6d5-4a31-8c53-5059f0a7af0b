// pages/index/index.js
const api = require('../../utils/api.js')
const darkMode = require('../../utils/darkMode.js')

Page({
  data: {
    userInfo: {},
    totalBalance: '0.00',
    monthlyIncome: '0.00',
    monthlyExpense: '0.00',
    currentDate: '',
    recentTransactions: [],
    loading: false,
    hideAmounts: false,
    isDarkMode: false
  },

  onLoad() {
    console.log('首页加载')
    // 应用深色模式
    darkMode.applyDarkModeToPage(this)
    this.initPage()
  },

  onShow() {
    console.log('首页显示')
    // 重新应用深色模式（可能在设置页面被修改）
    darkMode.applyDarkModeToPage(this)
    this.loadHideAmountsState()
    this.loadUserInfo()
    this.loadData()
  },

  onPullDownRefresh() {
    console.log('下拉刷新')
    this.loadData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化页面
  initPage() {
    const now = new Date()
    this.setData({
      currentDate: now.toLocaleDateString()
    })
  },

  // 加载用户信息
  loadUserInfo() {
    // 首先尝试从全局数据获取
    const app = getApp()
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      })
      return
    }

    // 如果全局数据没有，从本地存储获取
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      try {
        const parsed = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo
        this.setData({
          userInfo: parsed
        })
        // 同时更新全局数据
        app.globalData.userInfo = parsed
      } catch (error) {
        console.error('首页：解析用户信息失败', error)
      }
    }
  },

  // 加载数据
  async loadData() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      // 并行加载仪表盘数据和最近交易
      const [dashboardData, recentTransactions] = await Promise.all([
        api.analytics.getDashboard(),
        api.transactions.getRecent(5)
      ])

      console.log('仪表盘数据:', dashboardData)
      console.log('最近交易数据:', recentTransactions)

      // 处理仪表盘数据 - 根据后端实际返回的字段调整
      this.setData({
        totalBalance: this.formatAmount(dashboardData.netWorth || 0),
        monthlyIncome: this.formatAmount(0), // 暂时设为0，后续通过月度统计获取
        monthlyExpense: this.formatAmount(0)  // 暂时设为0，后续通过月度统计获取
      })

      // 处理最近交易数据 - 检查数据格式
      let transactions = []
      if (Array.isArray(recentTransactions)) {
        transactions = recentTransactions
      } else if (dashboardData.recentTransactions && Array.isArray(dashboardData.recentTransactions)) {
        transactions = dashboardData.recentTransactions
      }

      const formattedTransactions = transactions.map(transaction => {
        const transactionType = transaction.type || transaction.transaction_type

        // 处理分类显示
        let categoryDisplay = transaction.category_name || transaction.category
        if (!categoryDisplay || categoryDisplay === 'null') {
          // 如果没有分类，根据交易类型设置默认分类
          if (transactionType === 'transfer') {
            categoryDisplay = '转账'
          } else if (transactionType === 'income') {
            categoryDisplay = '收入'
          } else if (transactionType === 'expense') {
            categoryDisplay = '支出'
          } else {
            categoryDisplay = '未分类'
          }
        }

        return {
          id: transaction.id,
          type: transactionType,
          icon: this.getTransactionIcon(categoryDisplay, transactionType),
          description: transaction.description,
          category: categoryDisplay,
          amountText: this.formatTransactionAmount(transaction.amount, transactionType),
          date: this.formatTransactionDate(transaction.date || transaction.transaction_date)
        }
      })

      this.setData({
        recentTransactions: formattedTransactions
      })

      // 加载当月收支统计
      await this.loadMonthlyStats()

    } catch (error) {
      console.error('加载数据失败:', error)

      if (!error.message.includes('登录已过期')) {
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        })
      }
    } finally {
      this.setData({ loading: false })
      wx.stopPullDownRefresh()
    }
  },

  // 加载当月收支统计
  async loadMonthlyStats() {
    try {
      const now = new Date()
      const year = now.getFullYear()
      const month = now.getMonth() + 1

      const monthlyData = await api.analytics.getMonthlySummary(year, month)
      console.log('月度统计数据:', monthlyData)

      this.setData({
        monthlyIncome: this.formatAmount(monthlyData.total_income || 0),
        monthlyExpense: this.formatAmount(monthlyData.total_expense || 0)
      })
    } catch (error) {
      console.error('加载月度统计失败:', error)
      // 不显示错误提示，因为这是辅助数据
    }
  },

  // 格式化金额
  formatAmount(amount) {
    return parseFloat(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  },

  // 格式化交易金额
  formatTransactionAmount(amount, type) {
    const formattedAmount = this.formatAmount(Math.abs(amount))
    return type === 'income' ? `+¥${formattedAmount}` : `-¥${formattedAmount}`
  },

  // 获取交易图标
  getTransactionIcon(categoryName, type) {
    const iconMap = {
      '餐饮美食': '🍽️',
      '交通出行': '🚗',
      '购物消费': '🛍️',
      '生活服务': '🏠',
      '医疗健康': '🏥',
      '教育培训': '📚',
      '娱乐休闲': '🎮',
      '工资收入': '💰',
      '投资收益': '📈',
      '其他收入': '💵',
      '转账': '🔄'
    }

    return iconMap[categoryName] || (type === 'income' ? '💰' : '💸')
  },

  // 格式化交易日期
  formatTransactionDate(dateString) {
    const date = new Date(dateString)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    const transactionDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

    const timeStr = date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })

    if (transactionDate.getTime() === today.getTime()) {
      return `今天 ${timeStr}`
    } else if (transactionDate.getTime() === yesterday.getTime()) {
      return `昨天 ${timeStr}`
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit'
      }) + ` ${timeStr}`
    }
  },

  // 添加收入
  addIncome() {
    wx.navigateTo({
      url: '/pages/transactions/add?type=income'
    })
  },

  // 添加支出
  addExpense() {
    wx.navigateTo({
      url: '/pages/transactions/add?type=expense'
    })
  },

  // 添加转账
  addTransfer() {
    wx.navigateTo({
      url: '/pages/transactions/add?type=transfer'
    })
  },

  // 查看统计
  viewAnalytics() {
    wx.switchTab({
      url: '/pages/analytics/dashboard'
    })
  },

  // 查看更多交易
  viewMoreTransactions() {
    wx.switchTab({
      url: '/pages/transactions/list'
    })
  },

  // 查看账户详情
  viewAccountDetails() {
    wx.switchTab({
      url: '/pages/accounts/list'
    })
  },

  // 查看月度收入详情
  viewMonthlyIncome() {
    wx.switchTab({
      url: '/pages/analytics/dashboard'
    })
  },

  // 查看月度支出详情
  viewMonthlyExpense() {
    wx.switchTab({
      url: '/pages/analytics/dashboard'
    })
  },

  // 强制刷新数据
  async forceRefresh() {
    console.log('强制刷新数据')
    wx.showLoading({
      title: '刷新中...'
    })

    try {
      await this.loadData()
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('刷新失败:', error)
      wx.showToast({
        title: '刷新失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 加载隐藏金额状态
  loadHideAmountsState() {
    try {
      const hideAmounts = wx.getStorageSync('hideAmounts')
      this.setData({
        hideAmounts: hideAmounts === 'true' || hideAmounts === true
      })
    } catch (error) {
      console.error('加载隐藏金额状态失败:', error)
    }
  },

  // 切换金额显示/隐藏
  toggleAmountVisibility() {
    const newHideState = !this.data.hideAmounts
    this.setData({
      hideAmounts: newHideState
    })

    // 保存状态到本地存储
    try {
      wx.setStorageSync('hideAmounts', newHideState)
    } catch (error) {
      console.error('保存隐藏金额状态失败:', error)
    }
  }
})
