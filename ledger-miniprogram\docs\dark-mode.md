# 小程序深色模式实现说明

## 概述

本小程序已实现完整的深色模式功能，用户可以在偏好设置中切换深色/浅色主题。

## 功能特性

### 1. 深色模式切换
- 位置：设置 → 偏好设置 → 深色模式
- 支持实时切换，无需重启小程序
- 设置会同步到服务器并保存在本地

### 2. 全局主题支持
- 所有页面都支持深色模式
- 统一的颜色规范和视觉效果
- 符合iOS深色模式设计规范

### 3. 自动应用
- 页面加载时自动应用当前主题设置
- 页面切换时保持主题一致性
- 设置变更后立即生效

## 技术实现

### 1. 深色模式工具 (`utils/darkMode.js`)

提供以下功能：
- `isDarkMode()` - 检查当前是否为深色模式
- `setDarkMode(enabled)` - 设置深色模式状态
- `applyDarkModeToPage(page)` - 为页面应用深色模式
- `getDarkModeClass()` - 获取深色模式CSS类名

### 2. 页面集成

每个页面需要：
1. 引入深色模式工具
2. 在data中添加`isDarkMode`状态
3. 在`onLoad`和`onShow`中调用`applyDarkModeToPage`
4. 在WXML中绑定深色模式类名

示例代码：
```javascript
// 引入工具
const darkMode = require('../../utils/darkMode.js')

Page({
  data: {
    isDarkMode: false
  },
  
  onLoad() {
    darkMode.applyDarkModeToPage(this)
  },
  
  onShow() {
    darkMode.applyDarkModeToPage(this)
  }
})
```

```xml
<view class="page {{isDarkMode ? 'dark-mode' : ''}}">
  <!-- 页面内容 -->
</view>
```

### 3. 样式规范

深色模式颜色规范：
- 主背景色：`#1C1C1E`
- 卡片背景色：`#2C2C2E`
- 分割线颜色：`#3A3A3C`
- 主文字颜色：`#FFFFFF`
- 次要文字颜色：`#8E8E93`
- 强调色：`#0A84FF`
- 成功色：`#30D158`
- 错误色：`#FF453A`

## 已支持的页面

- ✅ 首页 (`pages/index`)
- ✅ 设置主页 (`pages/settings/index`)
- ✅ 偏好设置 (`pages/settings/preferences`)
- 🔄 其他页面待添加...

## 使用说明

### 用户操作
1. 打开小程序
2. 进入"设置"页面
3. 点击"偏好设置"
4. 开启"深色模式"开关
5. 返回任意页面查看效果

### 开发者添加新页面支持
1. 在页面JS文件中引入`darkMode`工具
2. 添加`isDarkMode`数据字段
3. 在生命周期函数中调用`applyDarkModeToPage`
4. 在WXML中添加类名绑定
5. 在WXSS中添加深色模式样式

## 注意事项

1. 深色模式设置会保存在本地存储和服务器
2. 页面切换时会自动重新应用主题设置
3. 所有颜色都使用`!important`确保优先级
4. 遵循iOS深色模式设计规范

## 故障排除

### 深色模式不生效
1. 检查页面是否正确引入`darkMode`工具
2. 确认`onLoad`和`onShow`中调用了`applyDarkModeToPage`
3. 验证WXML中是否正确绑定了类名
4. 检查WXSS中是否添加了深色模式样式

### 样式显示异常
1. 确保使用了正确的深色模式颜色
2. 检查CSS选择器优先级
3. 验证`!important`声明是否正确添加

## 更新日志

- 2024-08-12: 初始实现深色模式功能
- 支持首页、设置页面、偏好设置页面
- 创建深色模式工具类
- 建立统一的颜色规范
