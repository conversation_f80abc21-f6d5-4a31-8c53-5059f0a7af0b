<!-- 数据分析页面 -->
<view class="analytics-page {{isDarkMode ? 'dark-mode' : ''}}"
  <!-- 时间选择器 -->
  <view class="time-selector">
    <picker mode="date" fields="month" value="{{selectedMonth}}" bindchange="onMonthChange">
      <view class="month-picker">
        <text class="month-text">{{selectedMonthText}}</text>
        <text class="picker-arrow">▼</text>
      </view>
    </picker>

    <view class="header-actions">
      <view class="detail-btn" bindtap="goToDetailAnalysis">
        <text class="detail-icon">📊</text>
      </view>
      <view class="eye-toggle-btn" bindtap="toggleAmountVisibility">
        <text class="eye-icon">{{hideAmounts ? '👁️' : '🙈'}}</text>
      </view>
    </view>
  </view>

  <!-- 月度概览 -->
  <view class="monthly-overview">
    <view class="overview-item">
      <text class="overview-label">本月收入</text>
      <text class="overview-value income">{{hideAmounts ? '****' : '+¥' + monthlyIncome}}</text>
    </view>
    <view class="overview-item">
      <text class="overview-label">本月支出</text>
      <text class="overview-value expense">{{hideAmounts ? '****' : '-¥' + monthlyExpense}}</text>
    </view>
    <view class="overview-item">
      <text class="overview-label">本月结余</text>
      <text class="overview-value {{monthlyBalanceClass}}">{{hideAmounts ? '****' : monthlyBalanceText}}</text>
    </view>
  </view>

  <!-- 预算执行 -->
  <view class="budget-comparison">
    <view class="section-header">
      <text class="section-title">预算执行</text>
      <view class="budget-header-actions">
        <view class="budget-summary">
          <text class="budget-usage {{budgetUsageClass}}">
            {{budgetUsageRate}}%
          </text>
        </view>
        <view class="budget-manage-btn" bindtap="goToBudget">
          <text class="manage-text">预算管理</text>
        </view>
      </view>
    </view>

    <view wx:if="{{budgetLoading}}" class="loading">
      <text>加载中...</text>
    </view>

    <view wx:elif="{{budgetComparison.length === 0}}" class="empty-state">
      <text>暂无预算数据</text>
      <view class="empty-action">
        <text class="action-link" bindtap="goToBudget">设置预算</text>
      </view>
    </view>

    <view wx:else class="budget-list">
      <view class="budget-item" wx:for="{{budgetComparison}}" wx:key="id">
        <view class="budget-info">
          <text class="budget-icon">{{item.icon}}</text>
          <view class="budget-details">
            <text class="budget-name">{{item.name}}</text>
            <text class="budget-progress">
              {{hideAmounts ? '****' : '¥' + item.spentText}} / {{hideAmounts ? '****' : '¥' + item.budgetText}}
            </text>
          </view>
        </view>
        <view class="budget-status">
          <text class="usage-percent {{item.usageClass}}">
            {{item.usageRate}}%
          </text>
          <view class="budget-progress-bar">
            <view class="progress-fill {{item.usageClass}}"
                  style="width: {{item.progressWidth}}%"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 支出分类统计 -->
  <view class="category-stats">
    <view class="section-header">
      <text class="section-title">支出分类</text>
    </view>

    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>

    <view wx:elif="{{expenseCategories.length === 0}}" class="empty-state">
      <text>暂无支出数据</text>
    </view>

    <view wx:else class="categories-list">
      <view class="category-item" wx:for="{{expenseCategories}}" wx:key="id">
        <view class="category-info">
          <text class="category-icon">{{item.icon}}</text>
          <text class="category-name">{{item.name}}</text>
        </view>
        <view class="category-stats-info">
          <text class="category-amount">{{hideAmounts ? '****' : '¥' + item.amountText}}</text>
          <text class="category-percent">{{item.percentage}}%</text>
        </view>
        <view class="category-bar">
          <view class="bar-fill" style="width: {{item.percentage}}%"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 收入分类统计 -->
  <view class="category-stats">
    <view class="section-header">
      <text class="section-title">收入分类</text>
    </view>

    <view wx:if="{{incomeCategories.length === 0}}" class="empty-state">
      <text>暂无收入数据</text>
    </view>

    <view wx:else class="categories-list">
      <view class="category-item" wx:for="{{incomeCategories}}" wx:key="id">
        <view class="category-info">
          <text class="category-icon">{{item.icon}}</text>
          <text class="category-name">{{item.name}}</text>
        </view>
        <view class="category-stats-info">
          <text class="category-amount">{{hideAmounts ? '****' : '¥' + item.amountText}}</text>
          <text class="category-percent">{{item.percentage}}%</text>
        </view>
        <view class="category-bar">
          <view class="bar-fill income-bar" style="width: {{item.percentage}}%"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 月度趋势 -->
  <view class="trend-stats">
    <view class="section-header">
      <text class="section-title">近6个月趋势</text>
    </view>

    <view wx:if="{{trendLoading}}" class="loading">
      <text>加载中...</text>
    </view>

    <view wx:elif="{{trendData.length === 0}}" class="empty-state">
      <text>暂无趋势数据</text>
    </view>

    <view wx:else class="trend-chart">
      <view class="trend-legend">
        <view class="legend-item">
          <view class="legend-color income-color"></view>
          <text class="legend-text">收入</text>
        </view>
        <view class="legend-item">
          <view class="legend-color expense-color"></view>
          <text class="legend-text">支出</text>
        </view>
      </view>

      <view class="trend-bars">
        <view class="trend-month" wx:for="{{trendData}}" wx:key="month">
          <view class="month-bars">
            <view class="bar-container">
              <view class="income-bar-trend" style="height: {{item.incomeHeight}}%"></view>
              <view class="expense-bar-trend" style="height: {{item.expenseHeight}}%"></view>
            </view>
          </view>
          <text class="month-label">{{item.monthText}}</text>
          <view class="month-amounts" wx:if="{{!hideAmounts}}">
            <text class="income-amount">+{{item.incomeText}}</text>
            <text class="expense-amount">-{{item.expenseText}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
