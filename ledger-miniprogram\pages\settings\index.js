// pages/settings/index.js
const api = require('../../utils/api.js')
const darkMode = require('../../utils/darkMode.js')

Page({
  data: {
    userInfo: {},
    cacheSize: '计算中...',
    loading: false,
    isDarkMode: false
  },

  onLoad() {
    console.log('设置页面加载')
    // 应用深色模式
    darkMode.applyDarkModeToPage(this)
    this.loadUserInfo()
    this.updateCacheSize()
  },

  onShow() {
    // 每次显示页面时重新加载用户信息，确保数据同步
    // 重新应用深色模式（可能在偏好设置中被修改）
    darkMode.applyDarkModeToPage(this)
    this.loadUserInfo()
    this.updateCacheSize()
  },

  onReady() {
    // 页面渲染完成后，检查用户信息状态
    setTimeout(() => {
      if (!this.data.userInfo.username || this.data.userInfo.username === '' || this.data.userInfo.username === '未登录') {
        this.loadUserInfoFromServer()
      }
    }, 500) // 延迟500ms确保页面数据已设置
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      // 首先尝试从全局数据获取
      const app = getApp()
      if (app.globalData.userInfo) {
        this.setData({
          userInfo: app.globalData.userInfo
        })
        return
      }

      // 然后从本地存储获取用户信息
      const localUserInfo = wx.getStorageSync('userInfo')

      if (localUserInfo) {
        try {
          const parsedUserInfo = typeof localUserInfo === 'string' ? JSON.parse(localUserInfo) : localUserInfo

          this.setData({
            userInfo: parsedUserInfo
          })

          // 同时更新全局数据
          app.globalData.userInfo = parsedUserInfo

        } catch (parseError) {
          console.error('解析用户信息失败:', parseError)
          // 如果解析失败，尝试从服务器获取
          await this.loadUserInfoFromServer()
        }
      } else {
        // 如果没有本地用户信息，从服务器获取
        await this.loadUserInfoFromServer()
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
      // 设置默认状态
      this.setData({
        userInfo: {
          username: '未登录',
          email: ''
        }
      })
    }
  },

  // 从服务器加载用户信息
  async loadUserInfoFromServer() {
    try {
      const token = wx.getStorageSync('token')

      if (!token) {
        this.setData({
          userInfo: {
            username: '未登录',
            email: ''
          }
        })
        return
      }

      const response = await api.auth.getUserInfo()

      if (response && response.username) {
        // 更新本地存储、全局数据和页面数据
        const app = getApp()
        app.updateGlobalUserInfo(response)
        this.setData({
          userInfo: response
        })
      } else {
        this.setData({
          userInfo: {
            username: '数据异常',
            email: ''
          }
        })
      }
    } catch (error) {
      console.error('从服务器获取用户信息失败:', error)

      // 如果是401错误，可能需要重新登录
      if (error.message && error.message.includes('401')) {
        this.setData({
          userInfo: {
            username: '登录已过期',
            email: ''
          }
        })
      } else {
        // 其他错误，显示网络错误状态
        this.setData({
          userInfo: {
            username: '网络错误',
            email: ''
          }
        })
      }
    }
  },

  // 编辑个人信息
  editProfile() {
    wx.navigateTo({
      url: '/pages/settings/profile'
    })
  },

  // 个人信息
  goToProfile() {
    wx.navigateTo({
      url: '/pages/settings/profile'
    })
  },

  // 偏好设置
  goToPreferences() {
    wx.navigateTo({
      url: '/pages/settings/preferences'
    })
  },

  // 清除缓存
  clearCache() {
    // 计算缓存大小
    this.calculateCacheSize().then(cacheSize => {
      wx.showModal({
        title: '清除缓存',
        content: `当前缓存大小约 ${cacheSize}，确定要清除应用缓存吗？\n\n注意：这将清除所有本地数据，但不会影响您的登录状态。`,
        success: (res) => {
          if (res.confirm) {
            this.performClearCache()
          }
        }
      })
    })
  },

  // 计算缓存大小
  async calculateCacheSize() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      const sizeKB = storageInfo.currentSize

      if (sizeKB < 1024) {
        return `${sizeKB}KB`
      } else {
        return `${(sizeKB / 1024).toFixed(1)}MB`
      }
    } catch (error) {
      console.error('计算缓存大小失败:', error)
      return '未知'
    }
  },

  // 执行清除缓存
  performClearCache() {
    wx.showLoading({
      title: '清除中...'
    })

    try {
      // 保留重要数据
      const importantData = {
        token: wx.getStorageSync('token'),
        userInfo: wx.getStorageSync('userInfo'),
        preferences: wx.getStorageSync('preferences')
      }

      // 清除所有缓存
      wx.clearStorageSync()

      // 恢复重要数据
      Object.keys(importantData).forEach(key => {
        if (importantData[key]) {
          wx.setStorageSync(key, importantData[key])
        }
      })

      wx.hideLoading()
      wx.showToast({
        title: '缓存已清除',
        icon: 'success'
      })

      // 重新计算缓存大小（延迟执行以确保清除完成）
      setTimeout(() => {
        this.updateCacheSize()
      }, 1000)

    } catch (error) {
      wx.hideLoading()
      console.error('清除缓存失败:', error)
      wx.showToast({
        title: '清除失败',
        icon: 'none'
      })
    }
  },

  // 更新缓存大小显示
  async updateCacheSize() {
    const cacheSize = await this.calculateCacheSize()
    this.setData({
      cacheSize: cacheSize
    })
    console.log('当前缓存大小:', cacheSize)
  },

  // 使用帮助
  showHelp() {
    wx.showModal({
      title: '使用帮助',
      content: '📊 记账：点击"记录"标签页添加收支记录\n\n💳 账户：管理您的银行卡、现金等账户\n\n📈 分析：查看收支统计和趋势分析\n\n⚙️ 设置：个人信息和应用偏好设置\n\n如需更多帮助，请联系客服。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 关于应用
  goToAbout() {
    wx.navigateTo({
      url: '/pages/settings/about'
    })
  },

  // 调试页面
  goToDebug() {
    wx.navigateTo({
      url: '/pages/settings/debug'
    })
  },

  // 退出登录
  handleLogout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录信息和全局数据
          const app = getApp()
          app.clearGlobalUserInfo()
          wx.removeStorageSync('token')

          // 跳转到登录页
          wx.reLaunch({
            url: '/pages/login/login'
          })

          wx.showToast({
            title: '已退出登录',
            icon: 'none'
          })
        }
      }
    })
  },


})
