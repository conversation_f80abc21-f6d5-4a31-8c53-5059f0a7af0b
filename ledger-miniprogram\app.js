// app.js
App({
  onLaunch() {
    console.log('App Launch')

    // 检查登录状态
    const token = wx.getStorageSync('token')
    if (!token) {
      // 跳转到登录页
      wx.reLaunch({
        url: '/pages/login/login'
      })
    } else {
      // 如果有token，加载用户信息到全局
      this.loadGlobalUserInfo()
    }
  },

  onShow() {
    console.log('App Show')
  },

  onHide() {
    console.log('App Hide')
  },

  globalData: {
    userInfo: null
  },

  // 加载全局用户信息
  loadGlobalUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo')
      if (userInfo) {
        const parsed = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo
        this.globalData.userInfo = parsed
      }
    } catch (error) {
      console.error('加载全局用户信息失败:', error)
    }
  },

  // 更新全局用户信息
  updateGlobalUserInfo(userInfo) {
    this.globalData.userInfo = userInfo
    wx.setStorageSync('userInfo', JSON.stringify(userInfo))
  },

  // 清除全局用户信息
  clearGlobalUserInfo() {
    this.globalData.userInfo = null
    wx.removeStorageSync('userInfo')
  }
})
